rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read and write access to all documents until June 29, 2025
    match /{document=**} {
      allow read, write: if
          request.time < timestamp.date(2025, 6, 29);
    }
    
    // More specific rules for sessions collection
    match /sessions/{sessionId} {
      allow read, write: if
          request.time < timestamp.date(2025, 6, 29);
    }
    
    // Rules for user data collection (if needed in the future)
    match /users/{userId} {
      allow read, write: if
          request.time < timestamp.date(2025, 6, 29);
    }
  }
}
