MediBee – Your Tiny Medical Assistant A lightweight, AI-powered Progressive Web App (PWA) that simplifies medicine usage & medical jargon for Indian users. Built with privacy, simplicity, and accessibility in mind.

📌 Overview MediBee is a small medical helper designed for everyday users who struggle with complex medical instructions or terms. It uses Gemini AI (Text & Vision) to break down information and display it in clean, understandable language – including Hinglish 🇮🇳.

🎯 Key Goals: Simplify medicine usage & prescription info

Decode medical terms into simple, human-friendly language

Provide visuals & spoken guidance

Work well on low-end devices (PWA)

Ensure privacy-first data usage with optional IPFS + DIDs

⚙️ Features 🧠 Gemini AI integration – for real-time explanation & support

📷 Upload medicine images – and get quick feedback on use, dosage, etc.

🗣️ Hinglish + English output – easy-to-understand content

🛡️ Decentralized Identity (DID) + WebCrypto – protect user identity

🌐 IPFS support (optional) – store personal health notes securely

📱 PWA-ready – installable, mobile-friendly & offline-friendly

🔧 Tech Stack HTML + JS (Vanilla, Lightweight)

Gemini API (Text + Vision models)

Google Cloud (Vision OCR)

Web Crypto API (for encryption)

IPFS (via js-ipfs or web3.storage)

DID Protocol (light custom logic)

🚀 Getting Started bash Copy Edit git clone https://github.com/aditya4232/Skybee.git cd Skybee npm install # optional, if dependencies are added later npm run dev # or just open index.html directly for static use 📍 Use Cases Elderly users needing spoken guidance for medicines

Patients trying to understand prescriptions or diagnoses

Anyone needing simplified info on medical terms or tablets

Students building AI projects in healthcare + privacy

💡 Roadmap Build simple UI for image upload + text input

Integrate Gemini Pro + Vision

Add Hinglish mode toggle

Connect IPFS for saving user notes

Integrate lightweight DID auth

Polish UX, mobile view, dark mode

🤝 Contributing Want to improve MediBee or add features? PRs and suggestions are welcome! Feel free to fork, play around, and raise issues.

📜 License MIT License © 2025 Aditya Shenvi

🌐 Links 🔗 [Live Demo Coming Soon]
