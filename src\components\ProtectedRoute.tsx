import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSession } from './SessionProvider';
import LoadingSpinner from './LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSession?: boolean; // Make session requirement optional
}

const ProtectedRoute = ({ children, requireSession = false }: ProtectedRouteProps) => {
  const { hasActiveSession, isLoading, triggerSessionStart } = useSession();
  const navigate = useNavigate();

  useEffect(() => {
    // Only redirect if session is required and we're sure there's no session
    if (requireSession && !isLoading && !hasActiveSession) {
      console.log('ProtectedRoute: Session required but not found, triggering session start');
      triggerSessionStart();
    }
  }, [hasActiveSession, isLoading, requireSession, triggerSessionStart]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-slate-900 dark:via-blue-950 dark:to-slate-800 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading session..." />
      </div>
    );
  }

  // If session is required but not available, show the page anyway but trigger session popup
  if (requireSession && !hasActiveSession) {
    // Don't block access, just show the page and let session popup handle it
    return <>{children}</>;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
