
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 250 250;
    --foreground: 15 23 42;

    --card: 255 255 255;
    --card-foreground: 15 23 42;

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --primary: 59 130 246;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 51 65 85;

    --muted: 248 250 252;
    --muted-foreground: 100 116 139;

    --accent: 241 245 249;
    --accent-foreground: 51 65 85;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 51 65 85;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 241 245 249;
    --sidebar-accent-foreground: 51 65 85;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 59 130 246;

    /* Medical theme colors */
    --medical-blue: 59 130 246;
    --medical-green: 34 197 94;
    --medical-purple: 147 51 234;
    --medical-red: 239 68 68;
    --medical-amber: 245 158 11;
  }

  .dark {
    --background: 2 6 23;
    --foreground: 248 250 252;

    --card: 15 23 42;
    --card-foreground: 248 250 252;

    --popover: 15 23 42;
    --popover-foreground: 248 250 252;

    --primary: 59 130 246;
    --primary-foreground: 15 23 42;

    --secondary: 30 41 59;
    --secondary-foreground: 248 250 252;

    --muted: 30 41 59;
    --muted-foreground: 148 163 184;

    --accent: 30 41 59;
    --accent-foreground: 248 250 252;

    --destructive: 239 68 68;
    --destructive-foreground: 248 250 252;

    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 59 130 246;

    --sidebar-background: 15 23 42;
    --sidebar-foreground: 248 250 252;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 15 23 42;
    --sidebar-accent: 30 41 59;
    --sidebar-accent-foreground: 248 250 252;
    --sidebar-border: 30 41 59;
    --sidebar-ring: 59 130 246;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  /* Optimized glass effect with reduced blur */
  .glass {
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 16px rgba(31, 38, 135, 0.25);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  }

  /* Optimized backdrop blur utilities */
  .backdrop-blur-lg {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
  }

  .backdrop-blur-xl {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .backdrop-blur-2xl {
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
  }

  /* Medical gradient */
  .bg-medical-gradient {
    background: linear-gradient(135deg, rgb(59, 130, 246), rgb(147, 51, 234));
  }

  /* Medical colors */
  .text-medical-blue { color: rgb(59, 130, 246); }
  .text-medical-green { color: rgb(34, 197, 94); }
  .text-medical-purple { color: rgb(147, 51, 234); }
  .text-medical-red { color: rgb(239, 68, 68); }
  .text-medical-amber { color: rgb(245, 158, 11); }

  .bg-medical-blue\/20 { background-color: rgb(59, 130, 246, 0.2); }
  .bg-medical-green\/20 { background-color: rgb(34, 197, 94, 0.2); }
  .bg-medical-purple\/20 { background-color: rgb(147, 51, 234, 0.2); }
  .bg-medical-red\/20 { background-color: rgb(239, 68, 68, 0.2); }
  .bg-medical-amber\/20 { background-color: rgb(245, 158, 11, 0.2); }

  /* Optimized medical icons glow effect */
  .medical-glow {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.3));
  }

  .dark .medical-glow {
    filter: drop-shadow(0 0 12px rgba(6, 182, 212, 0.5));
  }

  /* Optimized neon shadow effects */
  .shadow-neon-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2);
  }

  .shadow-glass {
    box-shadow: 
      0 4px 16px rgba(31, 38, 135, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  .dark .shadow-glass {
    box-shadow: 
      0 4px 16px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(147, 51, 234, 0.6));
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.8));
  }

  /* Terminal-style text */
  .terminal-text {
    font-family: 'JetBrains Mono', monospace;
    letter-spacing: 0.05em;
  }

  /* Responsive optimizations */
  @media (max-width: 768px) {
    .glass {
      backdrop-filter: blur(8px) saturate(140%);
      -webkit-backdrop-filter: blur(8px) saturate(140%);
    }
    
    .medical-glow {
      filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.3));
    }

    .backdrop-blur-lg {
      backdrop-filter: blur(12px) saturate(160%);
      -webkit-backdrop-filter: blur(12px) saturate(160%);
    }
  }

  @media (max-width: 480px) {
    .glass {
      backdrop-filter: blur(6px) saturate(120%);
      -webkit-backdrop-filter: blur(6px) saturate(120%);
    }
  }

  /* Performance optimizations */
  * {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  /* Smooth touch interactions */
  button, a, [role="button"] {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Enhanced animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Focus styles for accessibility */
  .focus-visible:focus-visible {
    outline: 2px solid rgb(59, 130, 246);
    outline-offset: 2px;
  }
}
